import PageWrapper from '@/components/layout/PageWrapper'
import OrganicComposition from '@/components/compositions/OrganicComposition'
import {
  AnimatedSoftCircle,
  AnimatedRoundedRectangle,
  AnimatedTriangle,
  AnimatedSoftGrid,
  AnimatedBlob,
  AnimatedPill,
  AnimatedQuarterCircle,
  AnimatedHalfCircle
} from '@/components/shapes/AnimatedShapes'
import Link from 'next/link'

export default function Home() {
  return (
    <PageWrapper>
      {/* Hero Section */}
      <section className="relative px-6 md:px-12 lg:px-24 overflow-hidden flex items-center lg:h-[calc(100vh-6.5rem)]">
        <div className="max-w-7xl mx-auto w-full">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center">
            {/* Hero Content - Prioritized for LCP */}
            <div className="space-y-8">
              <h1 className="text-hero font-bold leading-none">
                What matters, made real.
              </h1>
              <p className="text-lg md:text-xl leading-relaxed text-gray-700 max-w-lg">
                Navhaus is a design and development studio that builds bold, efficient,
                and meaningful digital experiences — nothing more, nothing less.
              </p>
              <Link href="/contact" className="btn-red inline-block">
                Start Your Project
              </Link>
            </div>

            {/* Organic Geometric Composition - Deferred */}
            <div className="relative h-96 lg:h-[400px]">
              <OrganicComposition variant="hero" className="w-full h-full" />
            </div>
          </div>
        </div>
      </section>

      {/* How We Work Section */}
      <section className="px-6 md:px-12 lg:px-24 py-16 bg-brand-background">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-display font-bold text-center mb-16">How We Work</h2>

          <div className="space-y-24">
            {/* Process 1 - Left aligned */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div className="relative h-64 w-full">
                {/* Grid background */}
                <div className="absolute inset-0">
                  <AnimatedSoftGrid className="w-full h-full text-black" opacity="hero" animationPreset="subtle" animationIndex={0} />
                </div>
                {/* Main shape */}
                <div className="relative flex justify-center lg:justify-start items-center h-full">
                  <AnimatedSoftCircle size="xl" color="blue" className="w-32 h-32" animationPreset="gentle" animationIndex={1} />
                </div>
                {/* Dense decorative elements */}
                <div className="absolute top-4 right-8 opacity-80">
                  <AnimatedRoundedRectangle width="md" height="sm" color="yellow" className="w-12 h-8" animationPreset="flowing" animationIndex={2} />
                </div>
                <div className="absolute bottom-8 left-16 opacity-80">
                  <AnimatedSoftCircle size="md" color="red" className="w-10 h-10" animationPreset="gentle" animationIndex={3} />
                </div>
                <div className="absolute top-1/2 right-4 opacity-70">
                  <AnimatedTriangle size="md" color="red" direction="up" className="w-8 h-8" animationPreset="energetic" animationIndex={4} />
                </div>
                <div className="absolute top-8 left-8 opacity-60">
                  <AnimatedRoundedRectangle width="sm" height="sm" color="red" className="w-6 h-3" animationPreset="drift" animationIndex={5} />
                </div>
                <div className="absolute bottom-4 right-16 opacity-70">
                  <AnimatedSoftCircle size="sm" color="yellow" className="w-6 h-6" animationPreset="float" animationIndex={6} />
                </div>
                <div className="absolute top-16 right-20 opacity-50">
                  <AnimatedTriangle size="sm" color="blue" direction="up" className="w-4 h-4" animationPreset="pulse" animationIndex={7} />
                </div>
                <div className="absolute bottom-16 left-4 opacity-60">
                  <AnimatedRoundedRectangle width="sm" height="sm" color="blue" className="w-4 h-4" animationPreset="flowing" animationIndex={8} />
                </div>
                <div className="absolute top-1/3 left-1/3 opacity-50">
                  <AnimatedSoftCircle size="sm" color="yellow" className="w-4 h-4" animationPreset="drift" animationIndex={9} />
                </div>
              </div>
              <div className="space-y-6 text-center lg:text-left">
                <h3 className="text-heading font-bold">Listen</h3>
                <p className="text-body text-gray-700 leading-relaxed text-lg">
                  We start by understanding exactly what you're trying to do — and more importantly, why.
                </p>
              </div>
            </div>

            {/* Process 2 - Right aligned */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div className="space-y-6 text-center lg:text-right lg:order-1">
                <h3 className="text-heading font-bold">Design</h3>
                <p className="text-body text-gray-700 leading-relaxed text-lg">
                  We strip away noise, leaving only what needs to be there. Everything has a reason.
                </p>
              </div>
              <div className="relative h-64 w-full lg:order-2">
                {/* Grid background */}
                <div className="absolute inset-0">
                  <AnimatedSoftGrid className="w-full h-full text-black" opacity="hero" animationPreset="subtle" animationIndex={10} />
                </div>
                {/* Main shape */}
                <div className="relative flex justify-center lg:justify-end items-center h-full">
                  <AnimatedRoundedRectangle width="xl" height="xl" color="yellow" className="w-32 h-24" animationPreset="gentle" animationIndex={11} />
                </div>
                {/* Dense decorative elements */}
                <div className="absolute top-6 left-8 opacity-80">
                  <AnimatedSoftCircle size="lg" color="blue" className="w-14 h-14" animationPreset="energetic" animationIndex={12} />
                </div>
                <div className="absolute bottom-4 right-20 opacity-80">
                  <AnimatedTriangle size="md" color="red" direction="up" className="w-8 h-8" animationPreset="dynamic" animationIndex={13} />
                </div>
                <div className="absolute top-1/3 left-4 opacity-70">
                  <AnimatedRoundedRectangle width="sm" height="sm" color="blue" className="w-6 h-6" animationPreset="pulse" animationIndex={14} />
                </div>
                <div className="absolute bottom-1/3 left-1/3 opacity-70">
                  <AnimatedSoftCircle size="sm" color="red" className="w-6 h-6" animationPreset="flowing" animationIndex={15} />
                </div>
                <div className="absolute top-4 right-4 opacity-60">
                  <AnimatedTriangle size="sm" color="blue" direction="up" className="w-5 h-5" animationPreset="drift" animationIndex={16} />
                </div>
                <div className="absolute bottom-8 left-20 opacity-60">
                  <AnimatedRoundedRectangle width="sm" height="md" color="red" className="w-3 h-8" animationPreset="gentle" animationIndex={17} />
                </div>
                <div className="absolute top-20 left-16 opacity-50">
                  <AnimatedSoftCircle size="sm" color="yellow" className="w-4 h-4" animationPreset="float" animationIndex={18} />
                </div>
                <div className="absolute bottom-20 right-8 opacity-60">
                  <AnimatedTriangle size="sm" color="yellow" direction="up" className="w-3 h-3" animationPreset="pulse" animationIndex={19} />
                </div>
                <div className="absolute top-1/2 left-1/2 opacity-50">
                  <AnimatedRoundedRectangle width="sm" height="sm" color="blue" className="w-3 h-3" animationPreset="drift" animationIndex={20} />
                </div>
              </div>
            </div>

            {/* Process 3 - Left aligned */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div className="relative h-64 w-full">
                {/* Grid background */}
                <div className="absolute inset-0">
                  <AnimatedSoftGrid className="w-full h-full text-black" opacity="hero" animationPreset="subtle" animationIndex={21} />
                </div>
                {/* Building blocks composition */}
                <div className="relative flex justify-center lg:justify-start items-center h-full">
                  {/* Base foundation - larger and more prominent */}
                  <div className="absolute bottom-12">
                    <AnimatedRoundedRectangle width="xl" height="lg" color="red" className="w-40 h-12" animationPreset="flowing" animationIndex={22} />
                  </div>
                  {/* Middle layer */}
                  <div className="absolute bottom-20 left-4">
                    <AnimatedRoundedRectangle width="xl" height="lg" color="blue" className="w-32 h-12" animationPreset="energetic" animationIndex={23} />
                  </div>
                  {/* Top layer */}
                  <div className="absolute bottom-28 left-8">
                    <AnimatedRoundedRectangle width="lg" height="lg" color="yellow" className="w-24 h-12" animationPreset="pulse" animationIndex={24} />
                  </div>
                  {/* Connecting elements - larger and more visible */}
                  <div className="absolute bottom-16 right-4 opacity-80">
                    <AnimatedSoftCircle size="md" color="yellow" className="w-10 h-10" animationPreset="dynamic" animationIndex={25} />
                  </div>
                  <div className="absolute bottom-24 right-8 opacity-80">
                    <AnimatedSoftCircle size="sm" color="red" className="w-6 h-6" animationPreset="drift" animationIndex={26} />
                  </div>
                  <div className="absolute bottom-32 right-12 opacity-70">
                    <AnimatedTriangle size="sm" color="blue" direction="up" className="w-6 h-6" animationPreset="energetic" animationIndex={27} />
                  </div>
                  {/* Dense decorative elements */}
                  <div className="absolute top-8 right-4 opacity-70">
                    <AnimatedTriangle size="md" color="blue" direction="up" className="w-8 h-8" animationPreset="flowing" animationIndex={28} />
                  </div>
                  <div className="absolute top-16 left-4 opacity-60">
                    <AnimatedRoundedRectangle width="sm" height="sm" color="yellow" className="w-6 h-6" animationPreset="gentle" animationIndex={29} />
                  </div>
                  <div className="absolute top-4 left-12 opacity-60">
                    <AnimatedSoftCircle size="sm" color="red" className="w-6 h-6" animationPreset="float" animationIndex={30} />
                  </div>
                  <div className="absolute top-12 right-16 opacity-50">
                    <AnimatedRoundedRectangle width="sm" height="md" color="red" className="w-3 h-6" animationPreset="pulse" animationIndex={31} />
                  </div>
                  <div className="absolute top-20 left-16 opacity-50">
                    <AnimatedTriangle size="sm" color="yellow" direction="up" className="w-4 h-4" animationPreset="drift" animationIndex={32} />
                  </div>
                  <div className="absolute top-6 left-20 opacity-40">
                    <AnimatedSoftCircle size="sm" color="blue" className="w-4 h-4" animationPreset="float" animationIndex={33} />
                  </div>
                  <div className="absolute bottom-8 left-20 opacity-60">
                    <AnimatedRoundedRectangle width="sm" height="sm" color="yellow" className="w-4 h-4" animationPreset="flowing" animationIndex={34} />
                  </div>
                </div>
              </div>
              <div className="space-y-6 text-center lg:text-left">
                <h3 className="text-heading font-bold">Build</h3>
                <p className="text-body text-gray-700 leading-relaxed text-lg">
                  We ship clean code and scalable systems. Fast. Reliable. Yours to own.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Things We Love Building Section */}
      <section className="relative px-6 md:px-12 lg:px-24 py-16 md:py-24 overflow-hidden bg-bauhaus-black">
        <div className="max-w-7xl mx-auto relative z-10">
          <h2 className="text-display font-bold text-center mb-8 text-brand-background">Things We Love Building</h2>
          <p className="text-xl text-center text-gray-300 mb-16 max-w-3xl mx-auto">
            No big case studies (yet), but here's what gets us fired up.
          </p>

          {/* Asymmetric Mosaic Layout */}
          <div className="grid grid-cols-12 gap-6">
            {/* Large Feature Card - Marketing Sites */}
            <div className="col-span-12 md:col-span-7 lg:col-span-8 relative group">
              <div className="h-full bg-brand-background border-3 border-brand-background rounded-3xl p-8 md:p-12 flex flex-col justify-between relative overflow-hidden">
                {/* Background grid */}
                <div className="absolute inset-0 opacity-40">
                  <AnimatedSoftGrid className="w-full h-full text-black" opacity="default" animationPreset="subtle" animationIndex={35} />
                </div>

                {/* Content */}
                <div className="relative z-10">
                  <div className="inline-block bg-bauhaus-red text-white px-4 py-2 rounded-full text-sm font-bold mb-6">
                    PERFORMANCE
                  </div>
                  <h3 className="text-2xl md:text-3xl font-bold mb-4 text-bauhaus-black">High-performance marketing sites</h3>
                  <p className="text-lg text-gray-700 leading-relaxed max-w-lg">
                    Built to load fast, convert hard, and stay lean. Every element optimized for speed and results.
                  </p>
                </div>

                {/* Dynamic speed visualization */}
                <div className="absolute bottom-8 right-8 opacity-80">
                  <div className="relative">
                    <AnimatedSoftCircle size="xl" color="red" className="w-16 h-16" animationPreset="energetic" animationIndex={36} />
                    <div className="absolute -right-8 top-1/2 transform -translate-y-1/2">
                      <AnimatedRoundedRectangle width="lg" height="sm" color="yellow" className="w-12 h-2" animationPreset="flowing" animationIndex={37} />
                    </div>
                    <div className="absolute -right-12 top-1/2 transform -translate-y-1/2">
                      <AnimatedRoundedRectangle width="md" height="sm" color="yellow" className="w-8 h-2" animationPreset="flowing" animationIndex={38} />
                    </div>
                    <div className="absolute -right-16 top-1/2 transform -translate-y-1/2">
                      <AnimatedRoundedRectangle width="sm" height="sm" color="yellow" className="w-4 h-1" animationPreset="flowing" animationIndex={39} />
                    </div>
                  </div>
                </div>

                {/* Floating accent shapes */}
                <div className="absolute top-12 right-20 opacity-60">
                  <AnimatedTriangle size="sm" color="blue" direction="up" className="w-6 h-6" animationPreset="dynamic" animationIndex={40} />
                </div>
                <div className="absolute top-1/3 right-1/4 opacity-50">
                  <AnimatedSoftCircle size="sm" color="yellow" className="w-4 h-4" animationPreset="gentle" animationIndex={41} />
                </div>
              </div>
            </div>

            {/* Tall Card - Design Systems */}
            <div className="col-span-12 md:col-span-5 lg:col-span-4 relative">
              <div className="h-full bg-bauhaus-blue text-white border-3 border-bauhaus-blue rounded-3xl p-6 md:p-8 flex flex-col justify-between relative overflow-hidden">
                {/* Background grid */}
                <div className="absolute inset-0 opacity-30">
                  <AnimatedSoftGrid className="w-full h-full text-white" opacity="default" animationPreset="drift" animationIndex={42} />
                </div>

                {/* Content */}
                <div className="relative z-10">
                  <div className="inline-block bg-brand-background text-bauhaus-blue px-3 py-1 rounded-full text-xs font-bold mb-4">
                    SCALABLE
                  </div>
                  <h3 className="text-xl md:text-2xl font-bold mb-4">Design systems that scale</h3>
                  <p className="text-blue-100 leading-relaxed">
                    Modular components that grow with your needs. Interfaces that expand without chaos.
                  </p>
                </div>

                {/* Modular blocks visualization */}
                <div className="relative mt-8">
                  <div className="flex space-x-2 mb-2">
                    <AnimatedRoundedRectangle width="md" height="sm" color="yellow" className="w-8 h-6" animationPreset="gentle" animationIndex={43} />
                    <AnimatedRoundedRectangle width="md" height="sm" color="white" className="w-8 h-6" animationPreset="gentle" animationIndex={44} />
                  </div>
                  <div className="flex space-x-2 mb-2">
                    <AnimatedRoundedRectangle width="lg" height="sm" color="white" className="w-12 h-6" animationPreset="gentle" animationIndex={45} />
                    <AnimatedRoundedRectangle width="sm" height="sm" color="yellow" className="w-6 h-6" animationPreset="gentle" animationIndex={46} />
                  </div>
                  <div className="flex space-x-2">
                    <AnimatedRoundedRectangle width="sm" height="sm" color="yellow" className="w-6 h-6" animationPreset="gentle" animationIndex={47} />
                    <AnimatedRoundedRectangle width="md" height="sm" color="white" className="w-8 h-6" animationPreset="gentle" animationIndex={48} />
                    <AnimatedRoundedRectangle width="sm" height="sm" color="yellow" className="w-6 h-6" animationPreset="gentle" animationIndex={49} />
                  </div>
                </div>
              </div>
            </div>

            {/* Wide Card - Gutenberg Blocks */}
            <div className="col-span-12 md:col-span-8 relative">
              <div className="h-full bg-bauhaus-yellow text-bauhaus-black border-3 border-bauhaus-yellow rounded-3xl p-6 md:p-8 flex items-center relative overflow-hidden">
                {/* Background grid */}
                <div className="absolute inset-0 opacity-40">
                  <AnimatedSoftGrid className="w-full h-full text-black" opacity="default" animationPreset="pulse" animationIndex={50} />
                </div>

                {/* Content */}
                <div className="relative z-10 flex-1">
                  <div className="inline-block bg-bauhaus-black text-bauhaus-yellow px-3 py-1 rounded-full text-xs font-bold mb-4">
                    MODULAR
                  </div>
                  <h3 className="text-xl md:text-2xl font-bold mb-4">Custom Gutenberg blocks</h3>
                  <p className="text-gray-800 leading-relaxed max-w-md">
                    Purpose-built content blocks. No extra plugins, no bloat. Just exactly what you need.
                  </p>
                </div>

                {/* Block stack visualization */}
                <div className="relative ml-8 hidden md:block">
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <AnimatedSoftCircle size="sm" color="black" className="w-4 h-4" animationPreset="pulse" animationIndex={51} />
                      <AnimatedRoundedRectangle width="xl" height="sm" color="black" className="w-24 h-4" animationPreset="gentle" animationIndex={52} />
                    </div>
                    <div className="flex items-center space-x-2">
                      <AnimatedSoftCircle size="sm" color="black" className="w-4 h-4" animationPreset="pulse" animationIndex={53} />
                      <AnimatedRoundedRectangle width="lg" height="sm" color="black" className="w-20 h-4" animationPreset="gentle" animationIndex={54} />
                    </div>
                    <div className="flex items-center space-x-2">
                      <AnimatedSoftCircle size="sm" color="black" className="w-4 h-4" animationPreset="pulse" animationIndex={55} />
                      <AnimatedRoundedRectangle width="md" height="sm" color="black" className="w-16 h-4" animationPreset="gentle" animationIndex={56} />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Square Card - API Apps */}
            <div className="col-span-12 md:col-span-4 relative">
              <div className="h-full bg-brand-background border-3 border-brand-background rounded-3xl p-6 md:p-8 flex flex-col justify-center relative overflow-hidden">
                {/* Background grid */}
                <div className="absolute inset-0 opacity-40">
                  <AnimatedSoftGrid className="w-full h-full text-black" opacity="default" animationPreset="energetic" animationIndex={57} />
                </div>

                {/* Content */}
                <div className="relative z-10 text-center">
                  <div className="inline-block bg-bauhaus-red text-white px-3 py-1 rounded-full text-xs font-bold mb-4">
                    CONNECTED
                  </div>
                  <h3 className="text-lg md:text-xl font-bold mb-4 text-bauhaus-black">API-connected web apps</h3>
                  <p className="text-gray-700 leading-relaxed text-sm">
                    Lightweight, maintainable, and fast as hell.
                  </p>
                </div>

                {/* Connection visualization */}
                <div className="absolute inset-0 flex items-center justify-center opacity-60">
                  <div className="relative">
                    <AnimatedSoftCircle size="lg" color="red" className="w-12 h-12" animationPreset="pulse" animationIndex={58} />
                    <div className="absolute -top-6 -left-6">
                      <AnimatedRoundedRectangle width="sm" height="sm" color="blue" className="w-4 h-4" animationPreset="flowing" animationIndex={59} />
                    </div>
                    <div className="absolute -top-6 -right-6">
                      <AnimatedRoundedRectangle width="sm" height="sm" color="yellow" className="w-4 h-4" animationPreset="flowing" animationIndex={60} />
                    </div>
                    <div className="absolute -bottom-6 -left-6">
                      <AnimatedRoundedRectangle width="sm" height="sm" color="blue" className="w-4 h-4" animationPreset="flowing" animationIndex={61} />
                    </div>
                    <div className="absolute -bottom-6 -right-6">
                      <AnimatedRoundedRectangle width="sm" height="sm" color="yellow" className="w-4 h-4" animationPreset="flowing" animationIndex={62} />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Floating background elements */}
        <div className="absolute top-8 left-8 opacity-30">
          <AnimatedSoftCircle size="xl" color="blue" className="w-20 h-20" animationPreset="drift" animationIndex={63} />
        </div>
        <div className="absolute bottom-8 right-8 opacity-30">
          <AnimatedTriangle size="xl" color="red" direction="up" className="w-16 h-16" animationPreset="float" animationIndex={64} />
        </div>
        <div className="absolute top-1/2 right-4 opacity-20">
          <AnimatedRoundedRectangle width="lg" height="xl" color="yellow" className="w-8 h-24" animationPreset="gentle" animationIndex={65} />
        </div>
        <div className="absolute top-1/4 left-1/4 opacity-20">
          <AnimatedSoftCircle size="lg" color="yellow" className="w-16 h-16" animationPreset="float" animationIndex={66} />
        </div>
        <div className="absolute bottom-1/4 left-8 opacity-25">
          <AnimatedRoundedRectangle width="md" height="lg" color="blue" className="w-6 h-16" animationPreset="drift" animationIndex={67} />
        </div>
      </section>

      {/* Who We're For Section */}
      <section className="relative px-6 md:px-12 lg:px-24 pt-16 pb-8 bg-brand-background overflow-hidden">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-display font-bold mb-8">Who We're For</h2>
            <p className="text-xl text-gray-700 leading-relaxed max-w-3xl mx-auto">
              We work with founders, marketers, and teams who value clarity over chaos.
            </p>
          </div>

          {/* Large Grid Layout - Swiss/Bauhaus Style */}
          <div className="grid grid-cols-12 gap-6 max-w-6xl mx-auto">

            {/* Vertical Accent Block */}
            <div className="col-span-12 lg:col-span-4 bg-bauhaus-red text-white p-8 rounded-3xl flex flex-col justify-center">
              <div className="text-center">
                <div className="text-6xl md:text-7xl font-bold mb-4">01</div>
                <h4 className="text-xl font-bold mb-4">Founders</h4>
                <p className="text-red-100">Building something that matters</p>
              </div>
            </div>

            {/* Large Statement Block */}
            <div className="col-span-12 lg:col-span-8 bg-bauhaus-black text-brand-background p-8 md:p-12 rounded-3xl">
              <h3 className="text-3xl md:text-5xl font-bold leading-tight mb-6">
                People who want
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 text-lg">
                <div className="space-y-4">
                  <div className="flex items-start">
                    <div className="w-3 h-3 bg-bauhaus-red rounded-full mt-2 mr-4 flex-shrink-0"></div>
                    <span>Smaller teams, fewer meetings</span>
                  </div>
                  <div className="flex items-start">
                    <div className="w-3 h-3 bg-bauhaus-blue rounded-full mt-2 mr-4 flex-shrink-0"></div>
                    <span>Transparent communication</span>
                  </div>
                </div>
                <div className="space-y-4">
                  <div className="flex items-start">
                    <div className="w-3 h-3 bg-bauhaus-yellow rounded-full mt-2 mr-4 flex-shrink-0"></div>
                    <span>Clean design, clean code</span>
                  </div>
                  <div className="flex items-start">
                    <div className="w-3 h-3 bg-bauhaus-red rounded-full mt-2 mr-4 flex-shrink-0"></div>
                    <span>Something real, built fast</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Three Equal Blocks */}
            <div className="col-span-12 md:col-span-4 bg-bauhaus-blue text-white p-6 md:p-8 rounded-3xl text-center">
              <div className="text-5xl md:text-6xl font-bold mb-4">02</div>
              <h4 className="text-xl font-bold mb-4">Marketers</h4>
              <p className="text-blue-100">Performance that converts</p>
            </div>

            <div className="col-span-12 md:col-span-4 bg-bauhaus-yellow text-bauhaus-black p-6 md:p-8 rounded-3xl text-center">
              <div className="text-5xl md:text-6xl font-bold mb-4">03</div>
              <h4 className="text-xl font-bold mb-4">Team Leads</h4>
              <p className="text-gray-800">Clarity in every process</p>
            </div>

            <div className="col-span-12 md:col-span-4 bg-white border-3 border-bauhaus-black p-6 md:p-8 rounded-3xl text-center">
              <div className="text-5xl md:text-6xl font-bold mb-4 text-bauhaus-black">∞</div>
              <h4 className="text-xl font-bold mb-4">Scalability</h4>
              <p className="text-gray-700">Systems that grow with you</p>
            </div>
          </div>
        </div>

        {/* Bauhaus Background Elements - Large, Static, Geometric */}
        <div className="absolute top-20 right-20 w-40 h-40 border-8 border-bauhaus-red opacity-10"></div>
        <div className="absolute bottom-20 left-20 w-32 h-32 bg-bauhaus-blue opacity-5 rounded-full"></div>
        <div className="absolute top-1/2 left-10 w-4 h-40 bg-bauhaus-yellow opacity-10"></div>
        <div className="absolute top-40 right-1/4 w-20 h-20 bg-bauhaus-black opacity-5 transform rotate-45"></div>
      </section>

      {/* What We Build With Section - Interactive Tech Stack Visualization */}
      <section className="relative px-6 md:px-12 lg:px-24 py-16 md:py-24 bg-gradient-to-br from-brand-background via-gray-50 to-brand-background overflow-hidden">
        <div className="max-w-7xl mx-auto relative z-10">
          <div className="text-center mb-16">
            <h2 className="text-display font-bold mb-8">What We Build With</h2>
            <p className="text-xl text-gray-700 leading-relaxed max-w-4xl mx-auto">
              We use tools that are fast, modern, and built to scale. Everything is chosen for clarity, performance, and long-term maintainability — no fluff, no filler.
            </p>
          </div>

          {/* Central Hub Visualization */}
          <div className="relative max-w-6xl mx-auto">
            {/* Central Core */}
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-20">
              <div className="relative">
                <AnimatedSoftCircle size="xl" color="black" className="w-32 h-32 md:w-40 md:h-40" animationPreset="pulse" animationIndex={68} />
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center text-white">
                    <div className="text-lg md:text-xl font-bold">NAVHAUS</div>
                    <div className="text-xs md:text-sm opacity-80">STACK</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Orbiting Technology Clusters */}
            <div className="relative h-96 md:h-[500px] lg:h-[600px]">

              {/* Core WordPress Cluster - Top */}
              <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-4">
                <div className="relative group cursor-pointer">
                  <div className="bg-bauhaus-red text-white rounded-3xl p-6 md:p-8 shadow-lg border-3 border-bauhaus-red hover:scale-105 transition-transform duration-300">
                    <div className="text-center">
                      <AnimatedBlob color="yellow" className="w-8 h-8 mx-auto mb-3" animationPreset="gentle" animationIndex={69} />
                      <h3 className="font-bold text-lg mb-2">WordPress Core</h3>
                      <div className="text-sm space-y-1 opacity-90">
                        <div>Custom Themes</div>
                        <div>Sage + Blade</div>
                        <div>Gutenberg Blocks</div>
                      </div>
                    </div>
                  </div>
                  {/* Connecting lines */}
                  <div className="absolute top-full left-1/2 transform -translate-x-1/2">
                    <AnimatedPill color="red" className="w-1 h-16 md:h-20" animationPreset="drift" animationIndex={70} />
                  </div>
                </div>
              </div>

              {/* Frontend Cluster - Top Right */}
              <div className="absolute top-8 right-0 md:right-8 lg:right-16">
                <div className="relative group cursor-pointer">
                  <div className="bg-bauhaus-blue text-white rounded-3xl p-6 md:p-8 shadow-lg border-3 border-bauhaus-blue hover:scale-105 transition-transform duration-300">
                    <div className="text-center">
                      <AnimatedTriangle size="md" color="yellow" direction="up" className="w-8 h-8 mx-auto mb-3" animationPreset="dynamic" animationIndex={71} />
                      <h3 className="font-bold text-lg mb-2">Modern Frontend</h3>
                      <div className="text-sm space-y-1 opacity-90">
                        <div>React + Next.js</div>
                        <div>Alpine.js</div>
                        <div>Vite + ESBuild</div>
                      </div>
                    </div>
                  </div>
                  {/* Connecting lines */}
                  <div className="absolute bottom-8 left-0 transform -translate-x-8 rotate-45">
                    <AnimatedPill color="blue" className="w-1 h-12 md:h-16" animationPreset="flowing" animationIndex={72} />
                  </div>
                </div>
              </div>

              {/* Styling Cluster - Right */}
              <div className="absolute top-1/2 right-0 transform translate-x-4 -translate-y-1/2">
                <div className="relative group cursor-pointer">
                  <div className="bg-bauhaus-yellow text-bauhaus-black rounded-3xl p-6 md:p-8 shadow-lg border-3 border-bauhaus-yellow hover:scale-105 transition-transform duration-300">
                    <div className="text-center">
                      <AnimatedRoundedRectangle width="md" height="sm" color="black" className="w-8 h-6 mx-auto mb-3" animationPreset="gentle" animationIndex={73} />
                      <h3 className="font-bold text-lg mb-2">Styling</h3>
                      <div className="text-sm space-y-1 opacity-80">
                        <div>Tailwind CSS</div>
                        <div>PostCSS</div>
                        <div>Custom Design</div>
                      </div>
                    </div>
                  </div>
                  {/* Connecting lines */}
                  <div className="absolute top-1/2 left-0 transform -translate-x-8 -translate-y-1/2">
                    <AnimatedPill color="yellow" className="w-12 md:w-16 h-1" animationPreset="horizontal" animationIndex={74} />
                  </div>
                </div>
              </div>

              {/* Backend Cluster - Bottom Right */}
              <div className="absolute bottom-8 right-0 md:right-8 lg:right-16">
                <div className="relative group cursor-pointer">
                  <div className="bg-gray-800 text-white rounded-3xl p-6 md:p-8 shadow-lg border-3 border-gray-800 hover:scale-105 transition-transform duration-300">
                    <div className="text-center">
                      <AnimatedQuarterCircle corner="top-left" color="red" className="w-8 h-8 mx-auto mb-3" animationPreset="pulse" animationIndex={75} />
                      <h3 className="font-bold text-lg mb-2">Backend</h3>
                      <div className="text-sm space-y-1 opacity-90">
                        <div>PHP + MySQL</div>
                        <div>REST APIs</div>
                        <div>Headless Setup</div>
                      </div>
                    </div>
                  </div>
                  {/* Connecting lines */}
                  <div className="absolute top-0 left-8 transform -translate-y-8 -rotate-45">
                    <AnimatedPill color="black" className="w-1 h-12 md:h-16" animationPreset="energetic" animationIndex={76} />
                  </div>
                </div>
              </div>

              {/* Tooling Cluster - Bottom */}
              <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-4">
                <div className="relative group cursor-pointer">
                  <div className="bg-white text-bauhaus-black rounded-3xl p-6 md:p-8 shadow-lg border-3 border-bauhaus-black hover:scale-105 transition-transform duration-300">
                    <div className="text-center">
                      <AnimatedHalfCircle size="md" color="blue" direction="top" className="w-8 h-4 mx-auto mb-3" animationPreset="float" animationIndex={77} />
                      <h3 className="font-bold text-lg mb-2">Tooling</h3>
                      <div className="text-sm space-y-1 opacity-80">
                        <div>Git + GitHub</div>
                        <div>Composer + NPM</div>
                        <div>Figma + Docs</div>
                      </div>
                    </div>
                  </div>
                  {/* Connecting lines */}
                  <div className="absolute bottom-full left-1/2 transform -translate-x-1/2">
                    <AnimatedPill color="black" className="w-1 h-16 md:h-20" animationPreset="drift" animationIndex={78} />
                  </div>
                </div>
              </div>

              {/* Deployment Cluster - Bottom Left */}
              <div className="absolute bottom-8 left-0 md:left-8 lg:left-16">
                <div className="relative group cursor-pointer">
                  <div className="bg-green-600 text-white rounded-3xl p-6 md:p-8 shadow-lg border-3 border-green-600 hover:scale-105 transition-transform duration-300">
                    <div className="text-center">
                      <AnimatedBlob color="yellow" className="w-8 h-8 mx-auto mb-3" animationPreset="energetic" animationIndex={79} />
                      <h3 className="font-bold text-lg mb-2">Deployment</h3>
                      <div className="text-sm space-y-1 opacity-90">
                        <div>DigitalOcean</div>
                        <div>Vercel</div>
                        <div>Netlify</div>
                      </div>
                    </div>
                  </div>
                  {/* Connecting lines */}
                  <div className="absolute top-0 right-8 transform -translate-y-8 rotate-45">
                    <AnimatedPill color="yellow" className="w-1 h-12 md:h-16" animationPreset="flowing" animationIndex={80} />
                  </div>
                </div>
              </div>

              {/* Languages Cluster - Left */}
              <div className="absolute top-1/2 left-0 transform -translate-x-4 -translate-y-1/2">
                <div className="relative group cursor-pointer">
                  <div className="bg-purple-600 text-white rounded-3xl p-6 md:p-8 shadow-lg border-3 border-purple-600 hover:scale-105 transition-transform duration-300">
                    <div className="text-center">
                      <AnimatedSoftCircle size="md" color="yellow" className="w-8 h-8 mx-auto mb-3" animationPreset="pulse" animationIndex={81} />
                      <h3 className="font-bold text-lg mb-2">Languages</h3>
                      <div className="text-sm space-y-1 opacity-90">
                        <div>PHP</div>
                        <div>JavaScript</div>
                        <div>TypeScript</div>
                      </div>
                    </div>
                  </div>
                  {/* Connecting lines */}
                  <div className="absolute top-1/2 right-0 transform translate-x-8 -translate-y-1/2">
                    <AnimatedPill color="yellow" className="w-12 md:w-16 h-1" animationPreset="horizontal" animationIndex={82} />
                  </div>
                </div>
              </div>

              {/* Performance Cluster - Top Left */}
              <div className="absolute top-8 left-0 md:left-8 lg:left-16">
                <div className="relative group cursor-pointer">
                  <div className="bg-orange-500 text-white rounded-3xl p-6 md:p-8 shadow-lg border-3 border-orange-500 hover:scale-105 transition-transform duration-300">
                    <div className="text-center">
                      <AnimatedTriangle size="md" color="yellow" direction="right" className="w-8 h-8 mx-auto mb-3" animationPreset="energetic" animationIndex={83} />
                      <h3 className="font-bold text-lg mb-2">Performance</h3>
                      <div className="text-sm space-y-1 opacity-90">
                        <div>Optimization</div>
                        <div>Caching</div>
                        <div>Speed Focus</div>
                      </div>
                    </div>
                  </div>
                  {/* Connecting lines */}
                  <div className="absolute bottom-8 right-0 transform translate-x-8 rotate-45">
                    <AnimatedPill color="yellow" className="w-1 h-12 md:h-16" animationPreset="flowing" animationIndex={84} />
                  </div>
                </div>
              </div>

            </div>
          </div>

          {/* Bottom Philosophy Statement */}
          <div className="text-center mt-16 max-w-3xl mx-auto">
            <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-8 border border-gray-200">
              <h3 className="text-2xl font-bold mb-4 text-bauhaus-black">Our Philosophy</h3>
              <p className="text-lg text-gray-700 leading-relaxed">
                Every tool in our stack serves a purpose. We choose technologies that work together seamlessly,
                scale efficiently, and can be maintained long-term. No trends, no bloat — just proven tools
                that deliver results.
              </p>
            </div>
          </div>
        </div>

        {/* Floating Background Elements */}
        <div className="absolute top-16 left-16 opacity-20">
          <AnimatedSoftGrid className="w-32 h-32 text-black" opacity="default" animationPreset="drift" animationIndex={85} />
        </div>
        <div className="absolute bottom-16 right-16 opacity-20">
          <AnimatedSoftGrid className="w-40 h-40 text-black" opacity="default" animationPreset="float" animationIndex={86} />
        </div>
        <div className="absolute top-1/3 right-8 opacity-15">
          <AnimatedBlob color="blue" className="w-24 h-24" animationPreset="gentle" animationIndex={87} />
        </div>
        <div className="absolute bottom-1/3 left-8 opacity-15">
          <AnimatedQuarterCircle corner="bottom-right" color="red" className="w-20 h-20" animationPreset="drift" animationIndex={88} />
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="px-6 md:px-12 lg:px-24 py-24 md:py-32 bg-bauhaus-black text-bauhaus-white relative overflow-hidden">
        <div className="max-w-4xl mx-auto text-center relative z-10">
          <h2 className="text-display font-bold mb-6">Got something worth building?</h2>
          <p className="text-xl mb-12 leading-relaxed">
            Let's make it real. We'll help you strip it down to what matters — and bring it to life.
          </p>
          <Link href="/contact" className="btn-primary bg-brand-background text-bauhaus-black border-brand-background hover:bg-transparent hover:text-brand-background inline-block">
            Start Your Project
          </Link>
        </div>

        {/* Geometric motif with grids */}
        <div className="absolute inset-0 opacity-10">
          <OrganicComposition variant="feature" className="w-full h-full" />
        </div>
        <div className="absolute top-8 left-8 opacity-60">
          <div className="relative">
            <AnimatedSoftGrid className="w-32 h-32 text-black" animationPreset="drift" animationIndex={41} />
            <AnimatedSoftCircle size="xl" color="red" className="absolute top-4 left-4 w-20 h-20" animationPreset="energetic" animationIndex={42} />
          </div>
        </div>
        <div className="absolute bottom-8 right-8 opacity-60">
          <div className="relative">
            <AnimatedSoftGrid className="w-28 h-28 text-black" animationPreset="float" animationIndex={43} />
            <AnimatedRoundedRectangle width="xl" height="lg" color="yellow" className="absolute top-3 left-3 w-18 h-14" animationPreset="flowing" animationIndex={44} />
          </div>
        </div>
      </section>
    </PageWrapper>
  )
}
