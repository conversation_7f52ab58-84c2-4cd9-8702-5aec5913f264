import type { Metada<PERSON> } from 'next'
import { <PERSON> } from 'next/font/google'
import './globals.css'
import PerformanceMonitor from '@/components/performance/PerformanceMonitor'

// Optimize font loading with Next.js font optimization
const barlow = Barlow({
  subsets: ['latin'],
  weight: ['300', '400', '500', '600', '700', '800', '900'],
  display: 'swap', // Use font-display: swap for better LCP
  preload: true,
  variable: '--font-barlow',
})

export const metadata: Metadata = {
  title: 'navhaus | what matters, made real',
  description: 'Navhaus is a design and development studio that builds bold, efficient, and meaningful digital experiences — nothing more, nothing less.',
  icons: {
    icon: '/images/icon.png',
    shortcut: '/images/icon.png',
    apple: '/images/icon.png',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className={barlow.variable}>
      <head>
        {/* Preload critical resources for LCP optimization */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="" />
        {/* Preload critical CSS */}
        <link rel="preload" href="/fonts/barlow-v12-latin-700.woff2" as="font" type="font/woff2" crossOrigin="" />
        {/* DNS prefetch for any external resources */}
        <link rel="dns-prefetch" href="https://fonts.googleapis.com" />
      </head>
      <body className={barlow.className}>
        <PerformanceMonitor />
        {children}
      </body>
    </html>
  )
}
